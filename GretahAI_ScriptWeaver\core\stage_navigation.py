"""
Stage Navigation Component for GretahAI ScriptWeaver Sidebar.

This module provides the main navigation interface that allows users to:
1. Navigate directly to any accessible stage
2. See visual indicators for stage status
3. Get feedback about locked stages and prerequisites
4. Safely navigate while preserving state
"""

import streamlit as st
import logging
from typing import Optional
from state_manager import StateStage
from core.navigation_helpers import (
    get_all_stages_navigation_info,
    get_stage_visual_indicator,
    get_stage_button_style,
    format_stage_tooltip,
    validate_navigation_safety
)

# Set up logging
logger = logging.getLogger(__name__)


def render_stage_navigation(state) -> Optional[StateStage]:
    """
    Render the stage navigation component in the sidebar.

    Args:
        state: StateManager instance

    Returns:
        StateStage if user clicked a navigation button, None otherwise
    """
    try:
        # Add custom CSS for navigation buttons
        st.markdown("""
        <style>
        .nav-button-container {
            margin: 2px 0;
        }
        .nav-button-locked {
            opacity: 0.6;
        }
        .nav-button-current {
            border: 2px solid #FFD700 !important;
            background-color: rgba(255, 215, 0, 0.1) !important;
        }
        .nav-button-completed {
            border-left: 4px solid #28a745 !important;
        }
        .nav-button-available {
            border-left: 4px solid #ffc107 !important;
        }
        </style>
        """, unsafe_allow_html=True)

        # Get navigation info for all stages
        navigation_info = get_all_stages_navigation_info(state)

        # Create navigation section
        st.markdown("### 🧭 Stage Navigation")
        st.markdown("*Click to jump to any accessible stage*")

        # Create navigation buttons for each stage
        target_stage = None

        # Group stages for better organization
        # Home page - Always accessible
        st.markdown("#### 🏠 Navigation Hub")
        _render_single_stage_button(StateStage.HOME, navigation_info, state, always_accessible=True)

        st.markdown("---")

        _render_stage_group(
            "📁 Setup & Configuration",
            [StateStage.STAGE1_UPLOAD, StateStage.STAGE2_WEBSITE, StateStage.STAGE3_CONVERT],
            navigation_info,
            state
        )

        _render_stage_group(
            "🔍 Test Preparation",
            [StateStage.STAGE4_DETECT, StateStage.STAGE5_DATA],
            navigation_info,
            state
        )

        _render_stage_group(
            "⚙️ Script Generation & Execution",
            [StateStage.STAGE6_GENERATE, StateStage.STAGE7_EXECUTE, StateStage.STAGE8_OPTIMIZE],
            navigation_info,
            state
        )

        # Stage 9 (Script Browser) and Stage 10 (Script Playground) - Always accessible
        st.markdown("---")
        st.markdown("#### 📚 Independent Tools")
        _render_single_stage_button(StateStage.STAGE9_BROWSE, navigation_info, state, always_accessible=True)
        _render_single_stage_button(StateStage.STAGE10_PLAYGROUND, navigation_info, state, always_accessible=True)

        # Navigation is handled within the button click handlers
        # This function just renders the UI
        return None

    except Exception as e:
        logger.error(f"Error rendering stage navigation: {e}")
        st.error("⚠️ Navigation error occurred")
        return None


def _render_stage_group(group_title: str, stages: list, navigation_info: dict, state):
    """
    Render a group of navigation buttons with a collapsible section.

    Args:
        group_title: Title for the group
        stages: List of StateStage enums in this group
        navigation_info: Navigation info dictionary
        state: StateManager instance
    """
    with st.expander(group_title, expanded=True):
        for stage in stages:
            _render_stage_button(stage, navigation_info[stage], state)


def _render_single_stage_button(stage: StateStage, navigation_info: dict, state, always_accessible: bool = False):
    """
    Render a single stage button outside of a group.

    Args:
        stage: StateStage enum
        navigation_info: Navigation info dictionary
        state: StateManager instance
        always_accessible: Whether this stage is always accessible
    """
    if always_accessible:
        # Override accessibility for always-accessible stages
        accessibility_info = {
            'accessible': True,
            'status': 'available',
            'reason': 'Always accessible',
            'missing_prerequisites': []
        }
    else:
        accessibility_info = navigation_info.get(stage, {})

    _render_stage_button(stage, accessibility_info, state)


def _render_stage_button(stage: StateStage, accessibility_info: dict, state):
    """
    Render a single stage navigation button.

    Args:
        stage: StateStage enum
        accessibility_info: Accessibility info for this stage
        state: StateManager instance
    """
    # Get visual elements
    indicator = get_stage_visual_indicator(accessibility_info)
    button_type = get_stage_button_style(accessibility_info)
    stage_name = stage.get_display_name()

    # Create button label
    button_label = f"{indicator} {stage_name.replace('Stage ', '')}"

    # Create button key
    button_key = f"nav_to_{stage.value}"

    # Check if stage is accessible
    accessible = accessibility_info.get('accessible', False)

    # Create columns for button and info
    col1, col2 = st.columns([4, 1])

    with col1:
        if accessible:
            # Accessible stage - create clickable button
            if st.button(
                button_label,
                key=button_key,
                type=button_type,
                use_container_width=True,
                help=accessibility_info.get('reason', '')
            ):
                # Handle navigation click
                _handle_navigation_click(stage, state)
        else:
            # Locked stage - create disabled button
            st.button(
                button_label,
                key=f"{button_key}_disabled",
                disabled=True,
                use_container_width=True,
                help=accessibility_info.get('reason', '')
            )

    with col2:
        # Show info button for locked stages
        if not accessible:
            if st.button("ℹ️", key=f"{button_key}_info", help="Show prerequisites"):
                _show_prerequisites_modal(stage, accessibility_info)


def _handle_navigation_click(target_stage: StateStage, state):
    """
    Handle when a user clicks a navigation button.

    Args:
        target_stage: The stage to navigate to
        state: StateManager instance
    """
    try:
        # Validate navigation safety
        is_safe, warning_message = validate_navigation_safety(state, target_stage)

        if not is_safe:
            st.error(f"❌ Cannot navigate to {target_stage.get_display_name()}: {warning_message}")
            return

        # Show warning if there is one
        if warning_message:
            st.warning(warning_message)

        # Perform navigation
        current_stage = state.current_stage
        success = state.advance_to(
            target_stage,
            f"User navigation from {current_stage.get_display_name()} to {target_stage.get_display_name()}"
        )

        if success:
            # Set session state flag to trigger navigation (use different key to avoid widget conflict)
            st.session_state[f"navigation_completed_{target_stage.value}"] = True

            # Show success message
            st.success(f"✅ Navigating to {target_stage.get_display_name()}")

            # Log navigation
            logger.info(f"User navigated from {current_stage.get_display_name()} to {target_stage.get_display_name()}")

            # Trigger rerun to update UI
            st.rerun()
        else:
            st.error(f"❌ Failed to navigate to {target_stage.get_display_name()}")
            logger.error(f"Navigation failed from {current_stage.get_display_name()} to {target_stage.get_display_name()}")

    except Exception as e:
        logger.error(f"Error handling navigation to {target_stage}: {e}")
        st.error(f"❌ Navigation error: {str(e)}")


def _show_prerequisites_modal(stage: StateStage, accessibility_info: dict):
    """
    Show a modal with prerequisite information for a locked stage.

    Args:
        stage: The locked stage
        accessibility_info: Accessibility info for the stage
    """
    missing_prerequisites = accessibility_info.get('missing_prerequisites', [])

    # Use Streamlit's modal-like behavior with expander
    with st.expander(f"📋 Prerequisites for {stage.get_display_name()}", expanded=True):
        st.markdown("**To access this stage, you need to complete:**")

        for i, prerequisite in enumerate(missing_prerequisites, 1):
            st.markdown(f"{i}. {prerequisite}")

        st.markdown("---")
        st.markdown("💡 **Tip:** Complete the prerequisites in order by following the workflow from Stage 1.")


def render_navigation_summary(state):
    """
    Render a compact navigation summary showing current progress.

    Args:
        state: StateManager instance
    """
    try:
        # Get navigation info
        navigation_info = get_all_stages_navigation_info(state)

        # Count stages by status
        completed_count = sum(1 for info in navigation_info.values() if info['status'] == 'completed')
        available_count = sum(1 for info in navigation_info.values() if info['status'] == 'available')
        current_count = sum(1 for info in navigation_info.values() if info['status'] == 'current')
        locked_count = sum(1 for info in navigation_info.values() if info['status'] == 'locked')

        # Calculate progress
        total_stages = len(navigation_info)
        accessible_stages = completed_count + available_count + current_count
        progress_percentage = int((accessible_stages / total_stages) * 100)

        # Display summary
        st.markdown("#### 📊 Progress Summary")

        # Progress bar
        st.progress(progress_percentage / 100)
        st.markdown(f"**{accessible_stages}/{total_stages} stages accessible** ({progress_percentage}%)")

        # Status breakdown
        col1, col2 = st.columns(2)

        with col1:
            st.metric("✅ Completed", completed_count)
            st.metric("🔶 Current", current_count)

        with col2:
            st.metric("⚠️ Available", available_count)
            st.metric("⬜ Locked", locked_count)

        # Current stage info
        current_stage = state.current_stage
        st.markdown(f"**Current:** {current_stage.get_display_name()}")

    except Exception as e:
        logger.error(f"Error rendering navigation summary: {e}")
        st.error("⚠️ Could not load progress summary")


def handle_navigation_state_changes(state):
    """
    Handle any navigation-related state changes that need to be processed.

    Args:
        state: StateManager instance

    Returns:
        bool: True if navigation occurred, False otherwise
    """
    try:
        # Check for navigation flags in session state
        all_stages = [
            StateStage.STAGE1_UPLOAD,
            StateStage.STAGE2_WEBSITE,
            StateStage.STAGE3_CONVERT,
            StateStage.STAGE4_DETECT,
            StateStage.STAGE5_DATA,
            StateStage.STAGE6_GENERATE,
            StateStage.STAGE7_EXECUTE,
            StateStage.STAGE8_OPTIMIZE,
            StateStage.STAGE9_BROWSE,
            StateStage.STAGE10_PLAYGROUND
        ]

        for stage in all_stages:
            nav_flag = f"navigation_completed_{stage.value}"
            if st.session_state.get(nav_flag, False):
                # Clear the flag
                st.session_state[nav_flag] = False

                # Navigation already handled in _handle_navigation_click
                # Just return True to indicate navigation occurred
                return True

        return False

    except Exception as e:
        logger.error(f"Error handling navigation state changes: {e}")
        return False
