"""
Mode-Based Navigation System for GretahAI ScriptWeaver

This module provides a simplified three-mode navigation system that groups
the 10 stages into 3 logical modes:

1. Script Generation Mode (Stages 1-8): Sequential workflow for creating test scripts
2. Script Viewer Mode (Stage 9): Script browsing and management
3. Script Playground Mode (Stage 10): Template-based script experimentation

The goal is to simplify navigation by providing high-level mode switching
while maintaining all existing functionality.
"""

import streamlit as st
import logging
from typing import Dict, Any, Optional
from state_manager import StateStage

# Set up logging
logger = logging.getLogger(__name__)

# Define the three main application modes
class ApplicationMode:
    SCRIPT_GENERATION = "script_generation"
    SCRIPT_VIEWER = "script_viewer"
    SCRIPT_PLAYGROUND = "script_playground"

# Map stages to modes
STAGE_TO_MODE = {
    StateStage.HOME: ApplicationMode.SCRIPT_GENERATION,  # Home page defaults to script generation mode
    StateStage.STAGE1_UPLOAD: ApplicationMode.SCRIPT_GENERATION,
    StateStage.STAGE2_WEBSITE: ApplicationMode.SCRIPT_GENERATION,
    StateStage.STAGE3_CONVERT: ApplicationMode.SCRIPT_GENERATION,
    StateStage.STAGE4_DETECT: ApplicationMode.SCRIPT_GENERATION,
    StateStage.STAGE5_DATA: ApplicationMode.SCRIPT_GENERATION,
    StateStage.STAGE6_GENERATE: ApplicationMode.SCRIPT_GENERATION,
    StateStage.STAGE7_EXECUTE: ApplicationMode.SCRIPT_GENERATION,
    StateStage.STAGE8_OPTIMIZE: ApplicationMode.SCRIPT_GENERATION,
    StateStage.STAGE9_BROWSE: ApplicationMode.SCRIPT_VIEWER,
    StateStage.STAGE10_PLAYGROUND: ApplicationMode.SCRIPT_PLAYGROUND,
}

# Map modes to default stages
MODE_TO_DEFAULT_STAGE = {
    ApplicationMode.SCRIPT_GENERATION: StateStage.HOME,  # Start at home page for mode selection
    ApplicationMode.SCRIPT_VIEWER: StateStage.STAGE9_BROWSE,
    ApplicationMode.SCRIPT_PLAYGROUND: StateStage.STAGE10_PLAYGROUND,
}


def get_current_mode(state) -> str:
    """
    Get the current application mode based on the current stage.
    
    Args:
        state: StateManager instance
        
    Returns:
        str: Current application mode
    """
    current_stage = state.current_stage
    return STAGE_TO_MODE.get(current_stage, ApplicationMode.SCRIPT_GENERATION)


def render_mode_navigation(state):
    """
    Render the three-mode navigation toggle in the sidebar.
    
    Args:
        state: StateManager instance
    """
    try:
        st.markdown("### 🎯 Application Mode")
        
        # Get current mode
        current_mode = get_current_mode(state)
        
        # Define mode options with descriptions
        mode_options = {
            ApplicationMode.SCRIPT_GENERATION: "🔧 Script Generation",
            ApplicationMode.SCRIPT_VIEWER: "📜 Script Viewer", 
            ApplicationMode.SCRIPT_PLAYGROUND: "🎮 Script Playground"
        }
        
        # Create radio button for mode selection
        selected_mode = st.radio(
            "Choose your workflow mode:",
            options=list(mode_options.keys()),
            format_func=lambda x: mode_options[x],
            index=list(mode_options.keys()).index(current_mode),
            key="application_mode_selector"
        )
        
        # Handle mode change
        if selected_mode != current_mode:
            _handle_mode_change(state, selected_mode)
        
        # Display mode description
        _display_mode_description(selected_mode)
        
        # Show mode-specific navigation if in Script Generation mode
        if selected_mode == ApplicationMode.SCRIPT_GENERATION:
            _render_script_generation_navigation(state)
            
    except Exception as e:
        logger.error(f"Error rendering mode navigation: {e}")
        st.error("⚠️ Navigation error occurred")


def _handle_mode_change(state, new_mode: str):
    """
    Handle when user changes application mode.
    
    Args:
        state: StateManager instance
        new_mode: The new mode to switch to
    """
    try:
        # Get the default stage for the new mode
        target_stage = MODE_TO_DEFAULT_STAGE.get(new_mode, StateStage.STAGE1_UPLOAD)
        
        # For Script Generation mode, try to determine the most appropriate stage
        if new_mode == ApplicationMode.SCRIPT_GENERATION:
            target_stage = _determine_appropriate_generation_stage(state)
        
        # Perform the navigation
        current_stage = state.current_stage
        success = state.advance_to(
            target_stage,
            f"User switched from {STAGE_TO_MODE.get(current_stage, 'unknown')} mode to {new_mode} mode"
        )
        
        if success:
            logger.info(f"Mode changed from {STAGE_TO_MODE.get(current_stage, 'unknown')} to {new_mode}")
            st.success(f"✅ Switched to {new_mode.replace('_', ' ').title()} Mode")
            st.rerun()
        else:
            st.error(f"❌ Failed to switch to {new_mode.replace('_', ' ').title()} Mode")
            
    except Exception as e:
        logger.error(f"Error handling mode change to {new_mode}: {e}")
        st.error(f"❌ Mode change error: {str(e)}")


def _determine_appropriate_generation_stage(state) -> StateStage:
    """
    Determine the most appropriate stage for Script Generation mode based on current progress.

    Args:
        state: StateManager instance

    Returns:
        StateStage: The most appropriate stage to start with
    """
    # Check progress and return the most advanced accessible stage
    if hasattr(state, 'all_steps_done') and state.all_steps_done:
        return StateStage.STAGE8_OPTIMIZE
    elif hasattr(state, 'generated_script_path') and state.generated_script_path:
        return StateStage.STAGE7_EXECUTE
    elif ((hasattr(state, 'test_data') and state.test_data) or
          (hasattr(state, 'test_data_skipped') and state.test_data_skipped)):
        return StateStage.STAGE6_GENERATE
    elif hasattr(state, 'step_matches') and state.step_matches:
        return StateStage.STAGE5_DATA
    elif hasattr(state, 'selected_step') and state.selected_step:
        return StateStage.STAGE4_DETECT
    elif (hasattr(state, 'conversion_done') and state.conversion_done and
          hasattr(state, 'step_table_json') and state.step_table_json):
        return StateStage.STAGE3_CONVERT
    elif (hasattr(state, 'website_url') and state.website_url and
          state.website_url != "https://example.com"):
        return StateStage.STAGE2_WEBSITE
    elif (hasattr(state, 'test_cases') and state.test_cases) or \
         (hasattr(state, 'uploaded_excel') and state.uploaded_excel):
        return StateStage.STAGE1_UPLOAD
    else:
        return StateStage.HOME  # Start at home page if no progress


def _display_mode_description(mode: str):
    """
    Display a description of the selected mode.
    
    Args:
        mode: The selected application mode
    """
    descriptions = {
        ApplicationMode.SCRIPT_GENERATION: """
        **Sequential Workflow (Stages 1-8)**
        
        Complete end-to-end process for creating test scripts:
        • Upload CSV files with test cases
        • Configure website and convert test cases
        • Detect UI elements and configure test data
        • Generate, execute, and optimize scripts
        """,
        
        ApplicationMode.SCRIPT_VIEWER: """
        **Script Management & Browsing**
        
        Browse and manage all generated scripts:
        • View script history from all sessions
        • Compare different script versions
        • Download and organize scripts
        • Search and filter capabilities
        """,
        
        ApplicationMode.SCRIPT_PLAYGROUND: """
        **Experimental Script Generation**
        
        Template-based script experimentation:
        • Use optimized scripts as templates
        • Generate scripts for different test cases
        • Experiment with AI-powered adaptations
        • Preserve optimization patterns
        """
    }
    
    description = descriptions.get(mode, "Unknown mode")
    st.info(description)


def _render_script_generation_navigation(state):
    """
    Render detailed navigation for Script Generation mode.
    
    Args:
        state: StateManager instance
    """
    try:
        st.markdown("---")
        st.markdown("#### 📋 Generation Progress")
        
        # Calculate progress through the generation workflow
        generation_stages = [
            StateStage.STAGE1_UPLOAD,
            StateStage.STAGE2_WEBSITE,
            StateStage.STAGE3_CONVERT,
            StateStage.STAGE4_DETECT,
            StateStage.STAGE5_DATA,
            StateStage.STAGE6_GENERATE,
            StateStage.STAGE7_EXECUTE,
            StateStage.STAGE8_OPTIMIZE,
        ]
        
        current_stage_num = state.current_stage.get_stage_number()
        
        # Calculate completion
        completed_stages = 0
        for stage in generation_stages:
            stage_num = stage.get_stage_number()
            if stage_num < current_stage_num:
                completed_stages += 1
            elif stage_num == current_stage_num:
                # Check if current stage is actually completed
                if _is_stage_completed(state, stage):
                    completed_stages += 1
        
        # Display progress
        progress = completed_stages / len(generation_stages)
        st.progress(progress)
        st.markdown(f"**Progress:** {completed_stages}/{len(generation_stages)} stages ({int(progress * 100)}%)")
        
        # Show current stage
        current_stage_name = state.current_stage.get_display_name().replace("Stage ", "")
        st.markdown(f"**Current:** {current_stage_name}")
        
        # Quick navigation buttons for key stages
        with st.expander("🚀 Quick Navigation", expanded=False):
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("📁 Upload CSV", use_container_width=True):
                    state.advance_to(StateStage.STAGE1_UPLOAD, "Quick nav to Stage 1")
                    st.rerun()
                    
                if st.button("🎯 Select Test Case", use_container_width=True):
                    state.advance_to(StateStage.STAGE3_CONVERT, "Quick nav to Stage 3")
                    st.rerun()
            
            with col2:
                if st.button("⚙️ Generate Scripts", use_container_width=True):
                    state.advance_to(StateStage.STAGE6_GENERATE, "Quick nav to Stage 6")
                    st.rerun()
                    
                if st.button("🔧 Optimize Scripts", use_container_width=True):
                    state.advance_to(StateStage.STAGE8_OPTIMIZE, "Quick nav to Stage 8")
                    st.rerun()
        
    except Exception as e:
        logger.error(f"Error rendering script generation navigation: {e}")


def _is_stage_completed(state, stage: StateStage) -> bool:
    """
    Check if a specific stage is completed.
    
    Args:
        state: StateManager instance
        stage: The stage to check
        
    Returns:
        bool: True if stage is completed
    """
    if stage == StateStage.STAGE1_UPLOAD:
        return hasattr(state, 'test_cases') and state.test_cases
    elif stage == StateStage.STAGE2_WEBSITE:
        return (hasattr(state, 'website_url') and state.website_url and 
                state.website_url != "https://example.com")
    elif stage == StateStage.STAGE3_CONVERT:
        return (hasattr(state, 'conversion_done') and state.conversion_done and
                hasattr(state, 'step_table_json') and state.step_table_json)
    elif stage == StateStage.STAGE4_DETECT:
        return hasattr(state, 'selected_step') and state.selected_step
    elif stage == StateStage.STAGE5_DATA:
        return ((hasattr(state, 'test_data') and state.test_data) or
                (hasattr(state, 'test_data_skipped') and state.test_data_skipped))
    elif stage == StateStage.STAGE6_GENERATE:
        return hasattr(state, 'generated_script_path') and state.generated_script_path
    elif stage == StateStage.STAGE7_EXECUTE:
        return hasattr(state, 'all_steps_done') and state.all_steps_done
    elif stage == StateStage.STAGE8_OPTIMIZE:
        return hasattr(state, 'optimization_complete') and state.optimization_complete
    
    return False
