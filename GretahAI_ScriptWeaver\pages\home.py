"""
Home Page Component for GretahAI ScriptWeaver

This module provides the central navigation hub and landing page for the application.
It serves as the entry point where users select their workflow mode and optionally
prepare test case files.

Key Features:
- Mode selection between Script Generation and Script Playground workflows
- Professional welcome section with enterprise styling
- Optional test case file upload preparation
- Navigation to appropriate workflow stages based on user choice
- Maintains StateManager patterns and architectural consistency

This component was extracted from Stage 1 to create a clear separation between
the navigation hub functionality and the actual CSV ingestion workflow.
"""

import os
import logging
import tempfile
import streamlit as st
import pandas as pd
from pathlib import Path
from state_manager import StateStage

# Configure logging
logger = logging.getLogger("ScriptWeaver.home")

# Import helper functions
from core.excel_parser import parse_excel
from debug_utils import debug

def validate_uploaded_file(uploaded_file, file_content):
    """
    Validate uploaded file before processing.

    Args:
        uploaded_file: Streamlit uploaded file object
        file_content (bytes): Raw content of the Excel file

    Returns:
        list: List of validation errors (empty if valid)
    """
    errors = []

    # File size validation (50MB limit)
    if len(file_content) == 0:
        errors.append("File is empty")
    elif len(file_content) > 50 * 1024 * 1024:
        errors.append("File too large (max 50MB)")

    # File extension validation
    if not uploaded_file.name.lower().endswith('.xlsx'):
        errors.append("Invalid file extension (must be .xlsx)")

    # Basic Excel file signature check
    if len(file_content) >= 2 and not file_content.startswith(b'PK'):
        errors.append("File does not appear to be a valid Excel file")

    return errors

def safe_get_test_case_count(test_cases):
    """
    Safely get test case count with validation.

    Args:
        test_cases: Test cases data structure

    Returns:
        int: Number of test cases (0 if invalid)
    """
    if not test_cases:
        return 0
    if not isinstance(test_cases, list):
        debug(f"Warning: test_cases is not a list, type: {type(test_cases)}")
        return 0
    return len(test_cases)

@st.cache_data
def parse_excel_cached(file_content):
    """
    Cached version of parse_excel function with improved resource management.

    Args:
        file_content (bytes): Raw content of the Excel file

    Returns:
        list: List of test cases
    """
    debug("Using cached parse_excel function")
    temp_file_path = None

    try:
        # Create a temporary file to pass to parse_excel
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        # Parse the Excel file using the existing function
        test_cases = parse_excel(temp_file_path)
        return test_cases

    except Exception as e:
        debug(f"Error in cached parse_excel: {e}")
        logger.error(f"Error in cached parse_excel: {e}")
        raise
    finally:
        # Ensure cleanup even if parsing fails
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except Exception as cleanup_error:
                debug(f"Warning: Failed to cleanup temp file {temp_file_path}: {cleanup_error}")

def render_home_page(state):
    """
    Render the home page with central navigation hub functionality.
    
    This function provides the main landing page experience where users
    select their workflow mode and optionally prepare test case files.
    
    Args:
        state: StateManager instance
    """
    # Professional header with clean typography
    st.markdown("""
    <div style="margin-bottom: 2rem;">
        <h1 style="color: #1f2937; font-size: 2.5rem; font-weight: 600; margin-bottom: 0.5rem; letter-spacing: -0.025em;">
            GretahAI ScriptWeaver
        </h1>
        <p style="color: #6b7280; font-size: 1.125rem; margin-bottom: 0; font-weight: 400;">
            Automated Test Script Generation Platform
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Professional welcome section with enterprise styling
    st.markdown("""
    <div style="
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    ">
        <h3 style="
            color: #374151;
            font-size: 1.25rem;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 0.75rem;
            letter-spacing: -0.025em;
        ">
            Workflow Selection
        </h3>
        <p style="
            color: #6b7280;
            margin-bottom: 0;
            font-size: 1rem;
            line-height: 1.5;
        ">
            Select your preferred workflow mode to begin automated test script generation.
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Mode selection with prominent buttons
    _render_mode_selection(state)
    
    st.markdown("---")
    
    # Optional file upload section for preparation
    _render_file_upload_section(state)
    
    # Show current status if test cases are already loaded
    _render_current_status(state)

def _render_mode_selection(state):
    """Render the mode selection cards and buttons."""
    col1, col2 = st.columns(2)

    with col1:
        # Script Generation Mode card
        st.markdown("""
        <div style="
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 1.75rem;
            min-height: 180px;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.2s ease-in-out;
            position: relative;
            overflow: hidden;
        ">
            <div style="
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%);
            "></div>
            <h4 style="
                color: #1e293b;
                font-size: 1.25rem;
                font-weight: 700;
                margin-top: 0;
                margin-bottom: 1rem;
                letter-spacing: -0.025em;
                line-height: 1.2;
            ">
                Script Generation Mode
            </h4>
            <p style="
                color: #64748b;
                font-size: 0.95rem;
                line-height: 1.6;
                margin-bottom: 0;
                font-weight: 400;
            ">
                Complete end-to-end workflow for creating new test scripts from CSV/Excel files with automated UI detection and optimization.
            </p>
            <div style="
                position: absolute;
                bottom: 1rem;
                right: 1rem;
                width: 8px;
                height: 8px;
                background-color: #2563eb;
                border-radius: 50%;
                opacity: 0.3;
            "></div>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("<div style='margin-bottom: 1rem;'></div>", unsafe_allow_html=True)

        if st.button("Start Script Generation", key="start_script_generation", type="primary", use_container_width=True):
            # Navigate to Stage 2 to begin sequential workflow
            success = state.advance_to(StateStage.STAGE2_WEBSITE, "User selected Script Generation Mode from Home Page")
            if success:
                st.session_state['stage_progression_message'] = "Entering Script Generation Mode. Please configure your website URL."
                st.rerun()
                return
            else:
                st.error("Failed to enter Script Generation Mode. Please try again.")

    with col2:
        # Script Playground Mode card
        st.markdown("""
        <div style="
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 1.75rem;
            min-height: 180px;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.2s ease-in-out;
            position: relative;
            overflow: hidden;
        ">
            <div style="
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, #059669 0%, #10b981 100%);
            "></div>
            <h4 style="
                color: #1e293b;
                font-size: 1.25rem;
                font-weight: 700;
                margin-top: 0;
                margin-bottom: 1rem;
                letter-spacing: -0.025em;
                line-height: 1.2;
            ">
                Script Playground Mode
            </h4>
            <p style="
                color: #64748b;
                font-size: 0.95rem;
                line-height: 1.6;
                margin-bottom: 0;
                font-weight: 400;
            ">
                Work with existing optimized scripts as templates to generate new automation scripts using proven patterns and best practices.
            </p>
            <div style="
                position: absolute;
                bottom: 1rem;
                right: 1rem;
                width: 8px;
                height: 8px;
                background-color: #059669;
                border-radius: 50%;
                opacity: 0.3;
            "></div>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("<div style='margin-bottom: 1rem;'></div>", unsafe_allow_html=True)

        if st.button("Enter Script Playground", key="enter_script_playground", type="primary", use_container_width=True):
            # Navigate to Stage 10 for template management
            success = state.advance_to(StateStage.STAGE10_PLAYGROUND, "User selected Script Playground Mode from Home Page")
            if success:
                st.session_state['stage_progression_message'] = "Entering Script Playground Mode. Select templates and test cases to begin."
                st.rerun()
                return
            else:
                st.error("Failed to enter Script Playground Mode. Please try again.")

def _render_file_upload_section(state):
    """Render the optional file upload section for test case preparation."""
    with st.expander("Prepare Test Cases (Optional)", expanded=False):
        st.markdown("""
        **For Script Generation Mode:** You can optionally upload your test case file here to prepare for the workflow.
        This step can also be completed later in the Script Generation workflow.
        """)

        # Help text about Excel format
        show_format_help = st.checkbox("Show Excel Format Requirements", key="show_excel_format_help")
        if show_format_help:
            st.info("""
            **Required Excel Columns:**
            - **Test Case ID**: Unique identifier
            - **Test Case Objective**: Description of what is being tested
            - **Step No**: Step number
            - **Test Steps**: Action to perform
            - **Expected Result**: Expected outcome
            """)

        # File uploader
        uploaded_file = st.file_uploader("Select Excel file (.xlsx)", type=["xlsx"], key="excel_uploader")

        if uploaded_file is not None:
            _process_uploaded_file(state, uploaded_file)

def _process_uploaded_file(state, uploaded_file):
    """Process the uploaded Excel file."""
    try:
        # Get the file content
        file_content = uploaded_file.getvalue()

        # Validate uploaded file before processing
        validation_errors = validate_uploaded_file(uploaded_file, file_content)
        if validation_errors:
            for error in validation_errors:
                st.error(f"❌ {error}")
            return

        # Check if this is the same file we've already processed
        current_hash = hash(file_content)
        if hasattr(state, 'last_file_content_hash') and state.last_file_content_hash == current_hash:
            debug("File content unchanged - skipping reprocessing")
            logger.info("File content unchanged - skipping reprocessing")
            st.success(f"✅ File already processed: {uploaded_file.name}")
        else:
            debug("New or changed file detected - processing")
            logger.info("New or changed file detected - processing")

            # Update the content hash in state
            old_hash = getattr(state, 'last_file_content_hash', None)
            state.last_file_content_hash = current_hash
            debug(f"State change: last_file_content_hash = {current_hash} (was: {old_hash})")

            # Save the uploaded file to a temporary location
            temp_dir = Path("temp_uploads")
            temp_dir.mkdir(exist_ok=True)

            # Use a consistent filename based on the uploaded file name
            safe_filename = ''.join(c if c.isalnum() else '_' for c in uploaded_file.name)
            temp_file_path = temp_dir / f"test_cases_{safe_filename}"

            with open(temp_file_path, "wb") as f:
                f.write(file_content)

            # Update state with file information
            old_excel_path = getattr(state, 'uploaded_excel', None)
            state.uploaded_excel = str(temp_file_path)
            state.uploaded_file = str(temp_file_path)  # Backward compatibility
            debug(f"State change: uploaded_excel = {state.uploaded_excel} (was: {old_excel_path})")

            # Process file results
            _display_processing_results(state, uploaded_file, file_content, temp_file_path)

        # Always display a preview of the Excel file
        _display_file_preview(state)

    except Exception as e:
        debug(f"Error processing file: {e}")
        st.error(f"Error processing file: {e}")

def _display_processing_results(state, uploaded_file, file_content, temp_file_path):
    """Display the results of file processing."""
    st.markdown("**Processing Results:**")
    with st.container():
        # Verify the file was saved correctly
        if os.path.exists(temp_file_path) and os.path.getsize(temp_file_path) > 0:
            st.success(f"File uploaded: {uploaded_file.name}")

            # Parse the excel file using the cached function
            if parse_excel:
                try:
                    old_test_cases_count = safe_get_test_case_count(getattr(state, 'test_cases', None))
                    state.test_cases = parse_excel_cached(file_content)
                    new_test_cases_count = safe_get_test_case_count(state.test_cases)
                    debug(f"State change: test_cases count = {new_test_cases_count} (was: {old_test_cases_count})")

                    if new_test_cases_count == 0:
                        st.warning("No test cases found. Check file format.")
                    else:
                        st.success(f"Parsed {new_test_cases_count} test cases")
                        st.info("Test cases loaded successfully! Use the navigation buttons above to choose your workflow mode.")

                        # No automatic advancement - user must choose mode manually
                        debug(f"Test cases loaded in Home Page - no automatic advancement")
                        logger.info(f"Test cases loaded in Home Page, count: {new_test_cases_count} - awaiting user mode selection")

                except Exception as e:
                    debug(f"Error parsing file: {e}")
                    st.error(f"Error parsing file: {e}")
                    state.test_cases = None  # Ensure it's reset on error
            else:
                st.warning("Excel parsing function not available")
        else:
            st.error("Failed to save file")

def _display_file_preview(state):
    """Display a preview of the uploaded Excel file."""
    if hasattr(state, 'uploaded_excel') and os.path.exists(state.uploaded_excel):
        try:
            df = pd.read_excel(state.uploaded_excel)

            st.markdown("**File Preview:**")
            with st.container():
                # Show essential metric prominently
                if hasattr(state, 'test_cases'):
                    test_case_count = safe_get_test_case_count(state.test_cases)
                    if test_case_count > 0:
                        st.success(f"**{test_case_count} test cases** successfully parsed")
                    else:
                        st.warning("No test cases found in file")

                # Clean, focused data preview with toggle
                show_data_preview = st.checkbox("Show Data Preview", value=True, key="show_data_preview")
                if show_data_preview:
                    st.caption("First 10 rows:")
                    st.dataframe(df.head(10), use_container_width=True, hide_index=True)
        except Exception as e:
            debug(f"Error reading file for preview: {e}")
            st.error(f"Error reading file: {e}")

def _render_current_status(state):
    """Render current status if test cases are already loaded."""
    if hasattr(state, 'test_cases') and state.test_cases:
        test_case_count = safe_get_test_case_count(state.test_cases)
        if test_case_count > 0:
            st.success(f"**{test_case_count} test cases** already loaded from previous session")
            st.info("You can proceed with either workflow mode using the buttons above.")
